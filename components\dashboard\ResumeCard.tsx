import React, { useState, useMemo, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Trash2, Eye, FileText, X, Plus, Minus } from 'lucide-react';
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
  DialogDescription
} from '@/components/ui/dialog';
import { createClient } from '@/app/utils/supabase/client';
import { Resume } from '@/app/types/globalTypes';
import { PDFViewer, DocxViewer } from '@/components/resume/ViewerWrapper';

interface ResumeCardProps {
  resume: Resume;
  onDelete: (resume: Resume) => void;
  onSelect: (resume: Resume, isSelected: boolean) => void;
  isSelected: boolean;
  className?: string;
}

import ResumeAnalysisDialog from './ResumeAnalysisDialog';

const ResumeViewerDialog: React.FC<{
  resume: Resume;
  isOpen: boolean;
  onClose: () => void;
}> = ({ resume, isOpen, onClose }) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [scale, setScale] = useState(1);
  const supabase = createClient();

  useEffect(() => {
    const fetchResumeFile = async () => {
      if (isOpen && !fileUrl) {
        setIsLoading(true);
        try {
          const { data, error } = await supabase.storage
            .from('private-documents')
            .download(resume.resume);

          if (error) throw error;

          const blob = new Blob([data], {
            type:
              resume.file_type === 'pdf'
                ? 'application/pdf'
                : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          const url = URL.createObjectURL(blob);
          setFileUrl(url);
        } catch (error) {
          console.error('Error fetching file:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchResumeFile();
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [isOpen, resume, supabase, fileUrl]);

  const handleCloseModal = () => {
    onClose();
    if (fileUrl) {
      URL.revokeObjectURL(fileUrl);
      setFileUrl(null);
    }
    setScale(0.9);
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, 1.5));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, 0.4));
  };

  const handleFitToScreen = () => {
    setScale(0.9);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseModal}>
      <DialogContent className="max-w-7xl w-[98vw] h-[98vh] text-black p-0 overflow-hidden bg-gray-100">
        <DialogClose asChild>
          <button
            type="button"
            className="
              absolute top-2 right-2
              inline-flex items-center justify-center
              rounded-full p-1
              text-slate-400 hover:text-white
              hover:bg-slate-800
              focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
              transition-colors
            "
          >
            <X className="h-6 w-6" />
          </button>
        </DialogClose>
        <div className="flex flex-col h-full relative">
          {/* Fixed Navigation Bar */}
          <div className="fixed top-12 left-0 right-0 z-40 bg-white border-b shadow-sm">
            <div className="px-4 py-2 sm:px-6 flex items-center justify-between">
              <div className="flex-1 mr-8">
                <DialogTitle className="text-base sm:text-lg truncate">
                  {resume.file_name}
                </DialogTitle>
                <DialogDescription className="text-xs sm:text-sm text-gray-500">
                  View and zoom your CV document
                </DialogDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="hidden sm:flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomOut}
                    className="h-8 w-8 p-0"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleFitToScreen}
                    className="h-8 px-2 text-xs"
                  >
                    {Math.round(scale * 100)}%
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomIn}
                    className="h-8 w-8 p-0"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Mobile Controls */}
            <div className="sm:hidden flex items-center justify-center gap-2 p-2 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomOut}
                className="h-8 w-8 p-0"
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleFitToScreen}
                className="h-8 px-2 text-xs"
              >
                {Math.round(scale * 100)}%
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomIn}
                className="h-8 w-8 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content Area with Padding for Fixed Header */}
          <div
            className="absolute inset-0 overflow-auto bg-gray-100"
            style={{ top: '7rem' }}
          >
            {isLoading ? (
              <div className="flex justify-center items-center h-full">
                <p>Loading file content...</p>
              </div>
            ) : fileUrl ? (
              <div
                className="min-h-full p-4 flex justify-center pb-20 mt-12"
                style={{
                  transform: `scale(${scale})`,
                  transformOrigin: 'top center',
                  transition: 'transform 0.2s ease-in-out'
                }}
              >
                {resume.file_type === 'pdf' ? (
                  <PDFViewer fileUrl={fileUrl} />
                ) : (
                  <DocxViewer fileKey={resume.resume} />
                )}
              </div>
            ) : null}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const ResumeCard: React.FC<ResumeCardProps> = ({
  resume,
  onDelete,
  onSelect,
  isSelected,
  className
}) => {
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [showResume, setShowResume] = useState(false);

  const analysisResult = useMemo(() => {
    return resume.analysis_result || null;
  }, [resume.analysis_result]);

  return (
    <Card
      interactive
      className={`relative backdrop-blur-sm bg-white/10 border border-white/20 rounded-md shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300 ${
        isSelected
          ? 'ring-2 ring-[hsl(var(--hero-yellow))] ring-opacity-50'
          : ''
      } ${className || ''}`}
      onClick={() => onSelect(resume, !isSelected)}
    >
      <CardHeader className="p-3 sm:p-4">
        <CardTitle className="text-sm sm:text-base truncate pr-8 text-white">
          {resume.file_name}
        </CardTitle>
        <CardDescription className="text-xs text-slate-300">
          {new Date(resume.created_at ?? new Date()).toLocaleDateString()}
        </CardDescription>
      </CardHeader>

      <CardContent className="p-3 sm:p-4 pt-0">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="text-xs w-full cursor-pointer border-[hsl(var(--hero-yellow))] bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow))]/20 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))] hover:shadow-[0_0_10px_rgba(246,160,60,0.3)] transition-all duration-200 hover:scale-105"
            onClick={(e) => {
              e.stopPropagation();
              setShowResume(true);
            }}
          >
            <FileText className="mr-1.5 h-3.5 w-3.5" />
            View
          </Button>

          {analysisResult && (
            <Button
              variant="outline"
              size="sm"
              className="text-xs w-full cursor-pointer border-[hsl(var(--hero-yellow))] bg-[hsl(var(--hero-yellow))]/10 text-[hsl(var(--hero-yellow))] hover:bg-[hsl(var(--hero-yellow))]/20 hover:text-[hsl(var(--hero-yellow))] hover:border-[hsl(var(--hero-yellow))] hover:shadow-[0_0_10px_rgba(246,160,60,0.3)] transition-all duration-200 hover:scale-105"
              onClick={(e) => {
                e.stopPropagation();
                setShowAnalysis(true);
              }}
            >
              <Eye className="mr-1.5 h-3.5 w-3.5" />
              Analysis
            </Button>
          )}
        </div>
      </CardContent>

      <Button
        variant="ghost"
        size="sm"
        className="absolute top-2 right-1 p-1 text-white/70 hover:text-red-400 hover:bg-red-500/10 hover:shadow-[0_0_10px_rgba(239,68,68,0.3)] transition-all duration-200 hover:scale-105 cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          onDelete(resume);
        }}
      >
        <Trash2 className="h-3.5 w-3.5" />
      </Button>

      <ResumeAnalysisDialog
        analysis={analysisResult}
        isOpen={showAnalysis}
        onClose={() => setShowAnalysis(false)}
      />
      <ResumeViewerDialog
        resume={resume}
        isOpen={showResume}
        onClose={() => setShowResume(false)}
      />
    </Card>
  );
};

export default ResumeCard;
