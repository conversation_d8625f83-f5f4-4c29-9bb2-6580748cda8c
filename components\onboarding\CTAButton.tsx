'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import type { User } from '@supabase/supabase-js';
import { createClient } from '@/app/utils/supabase/client';

const CTAButton = () => {
  const [user, setUser] = useState<User | null>(null);
  const supabase = createClient();

  useEffect(() => {
    // Get initial auth state
    supabase.auth.getUser().then(({ data: { user } }) => {
      console.log('[CTAButton] Initial user state:', user);
      setUser(user);
    });

    // Subscribe to auth changes
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session) => {
      console.log('[CTAButton] Auth state changed:', {
        event: _event,
        user: session?.user
      });
      setUser(session?.user || null);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [supabase.auth]);

  // Encode the return URL properly
  const returnUrl = encodeURIComponent('/application-funnel?step=1');

  const ctaConfig = {
    text: user ? 'Get Started' : 'Unlock My Career Potential',
    href: user ? '/application-funnel?step=1' : `/signin?next=${returnUrl}`
  };

  console.log('[CTAButton] Current config:', ctaConfig);

  return (
    <Link
      href={ctaConfig.href}
      className="group px-8 py-4 md:py-5 bg-[hsl(var(--hero-yellow))] text-[hsl(var(--foreground))] font-semibold rounded-full shadow-lg inline-flex items-center justify-center gap-3 hover:bg-[hsl(var(--hero-yellow-light))] active:scale-95 text-[16px] md:text-[18px] whitespace-nowrap"
      onClick={() =>
        console.log(
          '[CTAButton] Button clicked, redirecting to:',
          ctaConfig.href
        )
      }
    >
      {ctaConfig.text}
      <ArrowRight className="w-5 h-5 md:w-6 md:h-6 group-hover:translate-x-1 transition-transform" />
    </Link>
  );
};

export default CTAButton;
