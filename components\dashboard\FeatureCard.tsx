import React, { useEffect, useState } from 'react';
import { ThemedButton } from '@/components/ui/themed-button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
} from '@/components/ui/card';
import { LucideIcon, Loader2, Coins } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FeatureCardProps {
  title: string;
  description: string;
  icon: string | LucideIcon;
  onActivate: () => void;
  disabled?: boolean;
  hasStoredResults?: boolean;
  isCareerMatching?: boolean;
  isResumeSelected?: boolean;
  isPending?: boolean;
  selectedJobId?: string | null;
  resumeId?: string | null;
  credits?: number;
}

const FeatureCardComponent: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  onActivate,
  disabled = false,
  hasStoredResults = false,
  isResumeSelected = false,
  isPending = false,
  selectedJobId = null,
  resumeId = null,
  credits
}) => {
  // Use state to manage client-side styles
  const [mounted, setMounted] = useState(false);
  const [buttonText, setButtonText] = useState<string>('Activate');

  // Set mounted state after initial render
  useEffect(() => {
    setMounted(true);
  }, []);

  // Initial (server) button class
  const buttonClass = 'w-full transition-all duration-300 hover:shadow-md';

  // Client-side button class
  const getClientButtonClass = () => {
    if (!mounted) return buttonClass;

    const baseClasses = 'w-full transition-all duration-300 hover:shadow-md';

    if (isPending) {
      return cn(baseClasses, 'animate-pulse');
    }

    // Consistent styling for "Show Results" buttons
    if (
      (hasStoredResults ||
        hasLocalStorageResults ||
        buttonText === 'Show Results') &&
      !disabled
    ) {
      return cn(
        baseClasses,
        'border-primary',
        'text-gray-900', // Dark text for better contrast on yellow/orange background
        'hover:bg-primary',
        'hover:text-primary-foreground'
      );
    }

    return baseClasses;
  };

  // Update button text
  // Replace the useEffect for button text in FeatureCard.tsx with this improved version

  // Add state to track if we've checked localStorage directly
  const [checkedLocalStorage, setCheckedLocalStorage] = useState(false);
  // Add state to track if we have found results in localStorage
  const [hasLocalStorageResults, setHasLocalStorageResults] = useState(false);

  // Function to check localStorage for this feature
  const checkForStoredResults = () => {
    if (!selectedJobId || !isResumeSelected) return false;

    try {
      // Get all localStorage keys
      const keys = Object.keys(localStorage);

      // Generate all possible variations of the feature name
      const featureKey = title.replace(/ /g, '_').toUpperCase();
      const normalizedTitle = title.replace(/-/g, ' ');
      const simplifiedTitle = normalizedTitle
        .replace(/[^a-zA-Z0-9]/g, '')
        .toLowerCase();
      const words = normalizedTitle.split(' ');

      // Import the mapping from feature titles to storage keys
      // This is the same mapping used in the Dashboard component
      const FEATURE_TO_STORAGE_KEY: Record<string, string> = {
        ATS: 'SimpleATS',
        'Skill Gap Analysis': 'SkillsGap',
        'AI Career Matching': 'CareerMatching',
        'Mock Interview': 'MockInterview',
        'Learning Resources': 'LearningResources',
        'Market Trends': 'MarketTrends',
        'AI CV Improvement': 'ResumeImprovement',
        'Cover Letter Generator': 'CoverLetter',
        'AI Career Coach': 'CareerCoach',
        'Recruitment Agencies': 'RecruitmentAgencies'
      };

      // Get the expected storage key format
      const storageKeyPrefix = FEATURE_TO_STORAGE_KEY[normalizedTitle];

      // Check for the exact key pattern that includes both job ID and resume ID
      // The expected format is: ${storageKeyPrefix}_${resumeId}_${jobId}
      const hasResults = keys.some((key) => {
        // Check if key is null or empty
        const value = localStorage.getItem(key);
        if (!value || value === 'undefined' || value.trim() === '') {
          return false;
        }

        // Check if this is a key for the current feature
        if (!storageKeyPrefix || !key.includes(storageKeyPrefix)) {
          return false;
        }

        // Check if key contains the job ID
        if (!key.includes(selectedJobId)) {
          return false;
        }

        // Split the key by underscore to check the pattern
        const parts = key.split('_');

        // The key should have at least 3 parts: storageKeyPrefix_resumeId_jobId
        if (parts.length < 3) {
          return false;
        }

        // The last part should be the job ID
        const lastPart = parts[parts.length - 1];
        if (lastPart !== selectedJobId) {
          return false;
        }

        // Check if the key ends with the pattern resumeId_jobId
        // This ensures we're only showing results for the specific resume and job combination
        return key.endsWith(`${resumeId}_${selectedJobId}`);
      });

      // Get matching keys for logging
      const matchingKeys = keys.filter((key) => {
        // Check if key is null or empty
        const value = localStorage.getItem(key);
        if (!value || value === 'undefined' || value.trim() === '') {
          return false;
        }

        // Check if this is a key for the current feature
        if (!storageKeyPrefix || !key.includes(storageKeyPrefix)) {
          return false;
        }

        // Check if key contains the job ID
        if (!key.includes(selectedJobId)) {
          return false;
        }

        // Split the key by underscore to check the pattern
        const parts = key.split('_');

        // The key should have at least 3 parts: storageKeyPrefix_resumeId_jobId
        if (parts.length < 3) {
          return false;
        }

        // The last part should be the job ID
        const lastPart = parts[parts.length - 1];
        if (lastPart !== selectedJobId) {
          return false;
        }

        // Check if the key ends with the pattern resumeId_jobId
        // This ensures we're only showing results for the specific resume and job combination
        return key.endsWith(`${resumeId}_${selectedJobId}`);
      });

      console.log(`FeatureCard localStorage check for ${title}:`, {
        featureKey,
        normalizedTitle,
        simplifiedTitle,
        words,
        hasResults,
        matchingKeys,
        allKeys: keys
      });

      return hasResults;
    } catch (e) {
      console.error('Error checking localStorage:', e);
      return false;
    }
  };

  // Listen for storage events
  useEffect(() => {
    // Create a debounced version of the update function
    let debounceTimer: NodeJS.Timeout | null = null;

    const updateUIIfNeeded = () => {
      // Clear any existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set a new timer to prevent rapid updates
      debounceTimer = setTimeout(() => {
        console.log(`FeatureCard ${title} - Checking localStorage (debounced)`);
        const hasResults = checkForStoredResults();

        if (hasResults && !hasLocalStorageResults) {
          console.log(
            `FeatureCard ${title} - Found results in localStorage, updating UI`
          );
          setHasLocalStorageResults(true);
          setButtonText('Show Results');
          setShowStoredResultsStyling(true);
        }
      }, 300); // 300ms debounce
    };

    const handleStorageEvent = () => {
      console.log(`FeatureCard ${title} - Storage event detected`);
      updateUIIfNeeded();
    };

    // Add event listener
    window.addEventListener('storage', handleStorageEvent);

    // Initial check
    updateUIIfNeeded();

    return () => {
      window.removeEventListener('storage', handleStorageEvent);
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [title, selectedJobId, isResumeSelected, hasLocalStorageResults]);

  // Initial check when component mounts - only runs once
  useEffect(() => {
    // Only run this once when the component mounts
    if (!checkedLocalStorage) {
      const hasResults = checkForStoredResults();

      if (hasResults) {
        console.log(
          `FeatureCard ${title} - Found results in localStorage on initial check`
        );
        setHasLocalStorageResults(true);
        setButtonText('Show Results');
        setShowStoredResultsStyling(true);
      }

      setCheckedLocalStorage(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update button text based on props and localStorage results
  useEffect(() => {
    // Create a debounced version of the update function
    let debounceTimer: NodeJS.Timeout | null = null;

    const updateButtonText = () => {
      // Clear any existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set a new timer to prevent rapid updates
      debounceTimer = setTimeout(() => {
        console.log(`Button text update for ${title}:`, {
          isPending,
          disabled,
          isResumeSelected,
          selectedJobId,
          hasStoredResults,
          hasLocalStorageResults
        });

        let newButtonText = '';

        if (isPending) {
          newButtonText = 'Analyzing...';
        } else if (disabled) {
          if (!isResumeSelected) {
            newButtonText = 'Select a CV';
          } else if (!selectedJobId) {
            newButtonText = 'Select a job';
          } else {
            // Both resume and job are selected, but feature is disabled
            // This is likely due to credit requirements
            newButtonText = 'Insufficient credits';
          }
        } else {
          // Check both prop and localStorage state
          const shouldShowResults = hasStoredResults || hasLocalStorageResults;
          newButtonText = shouldShowResults ? 'Show Results' : 'Activate';

          // Also update the styling
          if (shouldShowResults) {
            setShowStoredResultsStyling(true);
          }
        }

        // Only update if the text has changed
        if (newButtonText !== buttonText) {
          setButtonText(newButtonText);
        }
      }, 100); // 100ms debounce
    };

    // Call the update function
    updateButtonText();

    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [
    isPending,
    disabled,
    isResumeSelected,
    selectedJobId,
    hasStoredResults,
    hasLocalStorageResults,
    title,
    buttonText
  ]);

  const IconComponent =
    typeof icon === 'string'
      ? () => <span className="text-2xl mr-2">{icon}</span>
      : icon;

  const isButtonDisabled = disabled || isPending;

  // Add state to track if we should show the stored results styling
  const [showStoredResultsStyling, setShowStoredResultsStyling] = useState(
    hasStoredResults || hasLocalStorageResults
  );

  // Update styling when hasStoredResults changes or when we check localStorage
  useEffect(() => {
    // Only update if needed
    const shouldShowStyling =
      hasStoredResults ||
      hasLocalStorageResults ||
      buttonText === 'Show Results';

    if (shouldShowStyling && !showStoredResultsStyling) {
      console.log(`FeatureCard ${title} - Updating styling to show results`);
      setShowStoredResultsStyling(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasStoredResults, hasLocalStorageResults, buttonText]);

  return (
    <div className="pt-4">
      <Card
        interactive
        className={cn(
          'relative h-full flex flex-col',
          'backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg',
          (showStoredResultsStyling ||
            hasStoredResults ||
            hasLocalStorageResults) &&
            mounted &&
            'bg-gradient-to-b from-hero-yellow/5 to-transparent'
        )}
      >
        {credits !== undefined && (
          <div className="absolute -top-4 right-4 z-10">
            <div className="relative">
              <div className="absolute inset-0 bg-hero-bg/80 blur-sm rounded-full" />
              <div
                className={cn(
                  'relative px-3 py-1 rounded-full',
                  'border border-white/20',
                  'backdrop-blur-sm bg-white/10',
                  'flex items-center gap-1.5',
                  'text-sm font-medium',
                  'shadow-sm'
                )}
              >
                <Coins className="h-3.5 w-3.5 text-yellow-400" />
                <span className="text-yellow-400">{credits}</span>
                <span className="text-slate-200">credits</span>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-hidden flex flex-col h-full">
          <CardHeader className="space-y-2">
            <CardTitle className="flex items-center gap-2 text-xl font-roboto-condensed text-white">
              {typeof icon === 'string' ? (
                <span className="text-2xl">{icon}</span>
              ) : (
                <IconComponent className="h-6 w-6 text-yellow-400" />
              )}
              {title}
            </CardTitle>
            <CardDescription className="text-sm text-slate-200">
              {description}
            </CardDescription>
          </CardHeader>

          <CardContent className="flex-grow">
            {/* Additional content */}
          </CardContent>

          <CardFooter className="mt-auto pt-6">
            <ThemedButton
              className={cn(
                getClientButtonClass(),
                (showStoredResultsStyling ||
                  hasStoredResults ||
                  hasLocalStorageResults) &&
                  'bg-white/10 text-yellow-400 hover:bg-white/20 hover:text-yellow-300 border border-yellow-400/30'
              )}
              onClick={onActivate}
              disabled={isButtonDisabled}
              variant="primary"
              fullWidth={true}
              loading={isPending}
            >
              {buttonText}
            </ThemedButton>
          </CardFooter>
        </div>
      </Card>
    </div>
  );
};

// Export a memoized version of the component
export const FeatureCard = React.memo(FeatureCardComponent);
export default FeatureCard;
