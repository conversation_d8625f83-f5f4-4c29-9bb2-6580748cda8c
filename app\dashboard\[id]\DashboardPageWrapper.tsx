// app/dashboard/[id]/DashboardPageWrapper.tsx
'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';
import type { Resume, Job, FeatureProps } from 'app/types/globalTypes';
import type { PostgrestError } from '@supabase/supabase-js';

// ============================================================================
// LOADING COMPONENT
// ============================================================================
const DashboardLoadingSpinner = () => (
  <div className="relative isolate">
    <div className="mx-auto max-w-[1200px] px-4 pb-8 pt-20 space-y-8 text-white">
      <h1 className="font-roboto-condensed font-[700] text-[clamp(2rem,6vw,3rem)]">
        User{' '}
        <span className="text-yellow-400 text-shadow-yellow">Dashboard</span>
      </h1>

      <div className="flex flex-col items-center justify-center py-20 space-y-6">
        <div className="relative">
          <div className="absolute inset-0 rounded-full bg-hero-yellow/20 blur-xl animate-pulse"></div>
          <Loader2 className="h-16 w-16 animate-spin text-[#F6A03C] relative z-10" />
        </div>
        <div className="text-center space-y-2">
          <h3 className="text-2xl font-semibold text-white">
            Loading Dashboard
          </h3>
          <p className="text-slate-300">
            Preparing your personalized experience...
          </p>
        </div>
      </div>
    </div>
  </div>
);

// ============================================================================
// LAZY LOADED DASHBOARD DEPENDENCIES
// ============================================================================

// Lazy load the ENTIRE dashboard system - contexts, providers, everything
const DashboardSystem = dynamic(() => import('./DashboardSystem'), {
  ssr: false,
  loading: () => <DashboardLoadingSpinner />
}) as React.ComponentType<{
  userId: string;
  initialFeatures: FeatureProps[];
  initialResumes: Resume[];
  initialJobs: Job[];
  initialError: PostgrestError | null;
  initialCredits: number;
  dashboardId?: string;
}>;

// ============================================================================
// DASHBOARD PAGE WRAPPER (Route Entry Point)
// ============================================================================

export interface DashboardProps {
  userId: string;
  initialFeatures: FeatureProps[];
  initialResumes: Resume[];
  initialJobs: Job[];
  initialError: PostgrestError | null;
  initialCredits: number;
  dashboardId?: string;
}

export default function DashboardPageWrapper(props: DashboardProps) {
  return (
    <div className="min-h-screen bg-hero-bg">
      {/* Background blobs - lightweight, always loaded */}
      <div className="fixed inset-0 -z-20 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-[600px] h-[600px] rounded-full bg-hero-blue-glow blur-3xl animate-blob opacity-100"></div>
        <div className="absolute bottom-1/3 right-1/3 w-[700px] h-[700px] rounded-full bg-hero-purple-glow blur-3xl animate-blob delay-2000 opacity-100"></div>
        <div className="absolute top-2/3 left-1/2 w-[650px] h-[650px] rounded-full bg-hero-yellow-glow blur-3xl animate-blob delay-4000 opacity-100"></div>
        <div className="absolute top-1/3 right-1/4 w-[600px] h-[600px] rounded-full bg-hero-cyan-glow blur-3xl animate-blob delay-6000 opacity-100"></div>
      </div>

      {/* Lazy load the entire dashboard system */}
      <Suspense fallback={<DashboardLoadingSpinner />}>
        <DashboardSystem {...props} />
      </Suspense>
    </div>
  );
}
