import { ThemedButton } from '@/components/ui/themed-button';
import {
  Trash2,
  ExternalLink,
  Eye,
  Plus,
  ArrowRight,
  Zap,
  Target,
  CheckCircle
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Job } from '@/app/types/globalTypes';

interface JobSectionProps {
  savedJobs: Job[];
  selectedJob: Job | null;
  onViewDetails: (job: Job) => void;
  onDelete: (job: Job) => void;
  onSelect: (job: Job, isSelected: boolean) => void;
  onUploadJob?: () => void; // Made optional

  // Props for UploadJobForm
  onJobDataChange?: (desc: string, link: string) => void;
  onSubmit?: (desc: string, link: string) => Promise<Job>;
  initialJob?: Job | null;
  submitButtonText?: string;
  isLoading?: boolean;
  autoAdvance?: boolean;
  onComplete?: (savedJob: Job) => void;
}

export const JobSection: React.FC<JobSectionProps> = ({
  savedJobs,
  selectedJob,
  onViewDetails,
  onDelete,
  onSelect,
  onUploadJob
}) => {
  // If no saved jobs, show the main call-to-action prominently
  if (savedJobs.length === 0) {
    return (
      <div className="space-y-6">
        {/* Main Call-to-Action */}
        <div className="text-center space-y-4">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full mb-4">
            <Target className="w-8 h-8 text-gray-900" />
          </div>
          <h3 className="text-2xl font-semibold text-white">
            Ready to optimize your job application?
          </h3>
          <p className="text-lg text-slate-300 max-w-2xl mx-auto">
            Paste any job posting and get instant, personalized insights on how
            to improve your CV and cover letter
          </p>
        </div>

        {/* Benefits */}
        <div className="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
          <div className="backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg p-4 text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-lg mb-3">
              <Zap className="w-6 h-6 text-blue-400" />
            </div>
            <h4 className="font-semibold text-white mb-2">Instant Analysis</h4>
            <p className="text-sm text-slate-300">
              Get personalized CV feedback in under 60 seconds
            </p>
          </div>

          <div className="backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg p-4 text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-green-500/20 rounded-lg mb-3">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
            <h4 className="font-semibold text-white mb-2">ATS Optimized</h4>
            <p className="text-sm text-slate-300">
              Ensure your CV passes applicant tracking systems
            </p>
          </div>

          <div className="backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg p-4 text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-500/20 rounded-lg mb-3">
              <Target className="w-6 h-6 text-purple-400" />
            </div>
            <h4 className="font-semibold text-white mb-2">Job-Specific</h4>
            <p className="text-sm text-slate-300">
              Tailored advice for each role you apply to
            </p>
          </div>
        </div>

        {/* Primary CTA */}
        <div className="text-center space-y-4">
          {onUploadJob ? (
            <>
              <ThemedButton
                onClick={() => {
                  console.log('🔥 Add Your First Job clicked!');
                  console.log('onUploadJob function:', onUploadJob);
                  onUploadJob();
                }}
                variant="primary"
                size="lg"
                showArrow={true}
              >
                <Plus className="w-5 h-5 mr-2" />
                Add Your First Job
              </ThemedButton>
            </>
          ) : (
            <div className="text-center text-slate-300">
              <p>Upload functionality not available</p>
            </div>
          )}
          <p className="text-xs text-slate-400 mt-2">
            Find any job posting online and paste it here to get started
          </p>
        </div>

        {/* How it works */}
        <div className="border-t border-white/10 pt-6">
          <h4 className="text-lg font-semibold text-white text-center mb-4">
            How it works:
          </h4>
          <div className="flex flex-col md:flex-row items-center justify-center gap-4 text-sm text-slate-300">
            <div className="flex items-center gap-2">
              <span className="flex items-center justify-center w-6 h-6 bg-yellow-500 text-gray-900 rounded-full font-semibold text-xs">
                1
              </span>
              <span>Find a job on LinkedIn, Indeed, or any job site</span>
            </div>
            <ArrowRight className="w-4 h-4 text-slate-400 rotate-90 md:rotate-0" />
            <div className="flex items-center gap-2">
              <span className="flex items-center justify-center w-6 h-6 bg-yellow-500 text-gray-900 rounded-full font-semibold text-xs">
                2
              </span>
              <span>Copy and paste the job description</span>
            </div>
            <ArrowRight className="w-4 h-4 text-slate-400 rotate-90 md:rotate-0" />
            <div className="flex items-center gap-2">
              <span className="flex items-center justify-center w-6 h-6 bg-yellow-500 text-gray-900 rounded-full font-semibold text-xs">
                3
              </span>
              <span>Get personalized optimization tips</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If there are saved jobs, show them in a compact way
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-white">
            Saved{' '}
            <span className="text-yellow-400 text-shadow-yellow">Jobs</span>
            <span className="text-sm text-slate-400 ml-2">
              ({savedJobs.length})
            </span>
          </h2>
        </div>
        {onUploadJob && (
          <div className="flex items-center gap-2">
            <ThemedButton
              onClick={() => {
                console.log('🔥 Add Another Job clicked!');
                onUploadJob();
              }}
              size="sm"
              variant="primary"
            >
              <Plus className="h-4 w-4" />
              Add Another Job
            </ThemedButton>
          </div>
        )}
      </div>

      {/* Compact job list */}
      <div className="space-y-3 max-h-60 overflow-y-auto overflow-x-hidden">
        {savedJobs.map((job) => {
          const isSelected =
            selectedJob != null && String(job.id) === String(selectedJob.id);
          return (
            <div
              key={job.id}
              className={`backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg rounded-lg p-3 cursor-pointer transition-all duration-300 hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] ${
                isSelected
                  ? 'ring-2 ring-hero-yellow ring-inset bg-white/5'
                  : ''
              }`}
              onClick={() => onSelect(job, !isSelected)}
            >
              <div className="flex items-center justify-between gap-3 min-w-0">
                {/* Text content with proper truncation */}
                <div className="flex-1 min-w-0 overflow-hidden">
                  <h3
                    className="font-medium text-sm text-white truncate"
                    title={
                      job.title !== 'Untitled Job'
                        ? job.title || undefined
                        : job.description?.substring(0, 40) || 'Untitled Job'
                    }
                  >
                    {job.title !== 'Untitled Job'
                      ? job.title
                      : job.description?.substring(0, 40) || 'Untitled Job'}
                  </h3>
                  <p
                    className="text-xs text-slate-300 truncate"
                    title={
                      job.company && job.company !== 'Unknown Company'
                        ? job.company || undefined
                        : 'Company not specified'
                    }
                  >
                    {job.company && job.company !== 'Unknown Company'
                      ? job.company
                      : 'Company not specified'}
                  </p>
                </div>

                {/* Action buttons */}
                <div className="flex items-center gap-1 flex-shrink-0">
                  <ThemedButton
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs"
                    onClick={(e) => {
                      e?.stopPropagation();
                      onViewDetails(job);
                    }}
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    View
                  </ThemedButton>

                  <ThemedButton
                    variant="danger"
                    size="sm"
                    className="h-7 w-7 p-0"
                    onClick={(e) => {
                      e?.stopPropagation();
                      onDelete(job);
                    }}
                  >
                    <Trash2 className="h-3 w-3" />
                  </ThemedButton>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      {/* Quick add option for existing users */}
      <div className="border-t border-white/10 pt-3">
        {onUploadJob ? (
          <ThemedButton
            onClick={onUploadJob}
            variant="outline"
            fullWidth={true}
            className="border-dashed"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add another job to analyze
          </ThemedButton>
        ) : (
          <div className="w-full text-center py-4 text-slate-400 text-sm">
            Upload functionality not available
          </div>
        )}
      </div>
    </div>
  );
};

export default JobSection;
