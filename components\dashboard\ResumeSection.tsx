import React, { useEffect } from 'react';
import { ThemedButton } from '@/components/ui/themed-button';
import { Resume } from '@/app/types/globalTypes';
import { ResumeCard } from './ResumeCard';
import { Upload } from 'lucide-react';

interface ResumeAnalysisSectionProps {
  resumes: Resume[];
  selectedResume: Resume | null;
  onDelete: (resume: Resume) => void;
  onSelect: (resume: Resume, isSelected: boolean) => void;
  onUploadResume: () => void;
}

export const ResumeAnalysisSection: React.FC<ResumeAnalysisSectionProps> = ({
  resumes,
  selectedResume,
  onDelete,
  onSelect,
  onUploadResume
}) => {
  useEffect(() => {
    if (resumes.length > 0 && !selectedResume) {
      onSelect(resumes[0], true);
    }
  }, [resumes, selectedResume, onSelect]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-roboto-condensed font-semibold text-white">
            Your <span className="text-yellow-400 text-shadow-yellow">CVs</span>
          </h2>
        </div>
        <ThemedButton onClick={onUploadResume} size="sm" variant="primary">
          <Upload className="h-4 w-4" />
          Upload CV
        </ThemedButton>
      </div>

      {resumes.length > 0 ? (
        <div className="flex flex-col w-full space-y-4 mb-8">
          {resumes.map((resume) => (
            <div key={resume.id} className="w-full rounded-lg">
              <ResumeCard
                resume={resume}
                onDelete={onDelete}
                onSelect={onSelect}
                isSelected={selectedResume?.id === resume.id}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 flex flex-col items-center gap-4 backdrop-blur-sm bg-white/10 border border-white/20 rounded-lg p-6">
          <p className="text-slate-300">Upload your first CV to get started</p>
          <ThemedButton onClick={onUploadResume} variant="primary">
            <Upload className="h-4 w-4" />
            Upload CV
          </ThemedButton>
        </div>
      )}
    </div>
  );
};

export default ResumeAnalysisSection;
