import React, { useEffect } from 'react';
import { ThemedButton } from '@/components/ui/themed-button';
import { Resume } from '@/app/types/globalTypes';
import { ResumeCard } from './ResumeCard';
import { Upload, FileText } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface ResumeAnalysisSectionProps {
  resumes: Resume[];
  selectedResume: Resume | null;
  onDelete: (resume: Resume) => void;
  onSelect: (resume: Resume, isSelected: boolean) => void;
  onUploadResume: () => void;
}

export const ResumeAnalysisSection: React.FC<ResumeAnalysisSectionProps> = ({
  resumes,
  selectedResume,
  onDelete,
  onSelect,
  onUploadResume
}) => {
  useEffect(() => {
    if (resumes.length > 0 && !selectedResume) {
      onSelect(resumes[0], true);
    }
  }, [resumes, selectedResume, onSelect]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-roboto-condensed font-semibold text-white">
            Your <span className="text-yellow-400 text-shadow-yellow">CVs</span>
          </h2>
        </div>
        <ThemedButton onClick={onUploadResume} size="sm" variant="primary">
          <Upload className="h-4 w-4" />
          Upload CV
        </ThemedButton>
      </div>

      {resumes.length > 0 ? (
        <div className="flex flex-col w-full space-y-4 mb-8">
          {resumes.map((resume) => (
            <div key={resume.id} className="w-full rounded-lg">
              <ResumeCard
                resume={resume}
                onDelete={onDelete}
                onSelect={onSelect}
                isSelected={selectedResume?.id === resume.id}
              />
            </div>
          ))}
        </div>
      ) : (
        <Card className="bg-white/10 text-white overflow-hidden relative backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-[0_0_15px_rgba(246,160,60,0.15)] transition-all duration-300 rounded-xl">
          <CardContent className="pt-6 pb-6 relative">
            <div className="text-center space-y-5">
              <div className="flex items-center justify-center gap-2">
                <FileText className="h-5 w-5 text-[hsl(var(--hero-yellow))]" />
                <h2 className="text-lg font-medium text-white">Your CVs</h2>
              </div>

              <div className="relative py-4">
                <div className="absolute inset-0 bg-[hsl(var(--hero-yellow))]/10 blur-xl rounded-full" />
                <div className="relative flex flex-col items-center justify-center space-y-3">
                  <p className="text-slate-300">
                    Upload your first CV to get started
                  </p>
                  <ThemedButton onClick={onUploadResume} variant="primary">
                    <Upload className="h-4 w-4" />
                    Upload CV
                  </ThemedButton>
                </div>
              </div>

              <div className="w-full px-4 mt-4">
                <div className="border-t border-white/20 w-full"></div>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-2">
                <div className="flex items-center justify-center gap-2 text-slate-300 pt-3">
                  <Upload className="h-4 w-4 text-[hsl(var(--hero-yellow))]" />
                  <span className="text-sm">AI Analysis Ready</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-slate-300 pt-3">
                  <FileText className="h-4 w-4 text-[hsl(var(--hero-yellow))]" />
                  <span className="text-sm">Instant Feedback</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ResumeAnalysisSection;
