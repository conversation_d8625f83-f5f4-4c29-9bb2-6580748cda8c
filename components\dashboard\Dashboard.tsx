'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  Suspense
} from 'react';
import dynamic from 'next/dynamic';
import { Dialog } from '@/components/ui/dialog';
import { DialogCard } from '@/components/ui/dialog-card';
import { ThemedButton } from '../ui/themed-button';
import { Plus, Loader2 } from 'lucide-react';

// ALWAYS LOADED: Core sections that are immediately visible
import { ResumeAnalysisSection } from './ResumeSection';
import { JobSection } from './JobSection';
import { FeaturesSection } from './FeaturesSection';

// ALWAYS LOADED: Essential dialogs and utilities
import { UnifiedFeatureDialogs } from './UnifiedFeatureDialogs';
import { JobDetailsDialog } from './JobDetailsDialog';
import { DeleteDialog } from './DeleteDialog';
import { UploadResumeDialog } from './UploadResumeDialog';

// ALWAYS LOADED: Contexts & hooks
import { DashboardProvider, useDashboardContext } from './DashboardContext';
import {
  FeatureProvider,
  useFeature
} from '@/components/shared/FeatureContext';
import { useUnifiedFeatureHandlers } from '@/components/shared/useUnifiedFeatureHandlers';
import { useJobManagement } from '@/hooks/useJobManagement';

// ALWAYS LOADED: Shared ui & utils
import { CreditBalance } from 'components/credits/CreditBalance';
import { toast } from '@/hooks/use-toast';

// ALWAYS LOADED: Job upload form
import UploadJobForm from '../jobs/UploadJobForm';

// ALWAYS LOADED: Types
import type {
  Resume,
  Job,
  FeatureProps,
  JobAnalysisResult
} from 'app/types/globalTypes';
import type { PostgrestError } from '@supabase/supabase-js';

// ALWAYS LOADED: Constants
import { FEATURE_TO_STORAGE_KEY } from '@/app/types/featureTypes';
import { deleteJob, deleteResume } from './dashboardUtils';
import { SHOW_MARKET_TRENDS } from '@/lib/featureFlags';

// ============================================================================
// LAZY LOADED COMPONENTS - Only load when actually needed
// ============================================================================

// Lazy load heavy components that aren't needed immediately
const DirectRecruitmentAgenciesDialog = dynamic(
  () =>
    import('./DirectRecruitmentAgenciesDialog').then((mod) => ({
      default: mod.DirectRecruitmentAgenciesDialog
    })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-[#F6A03C]" />
      </div>
    )
  }
);

const DirectCareerCoachDialog = dynamic(
  () =>
    import('./DirectCareerCoachDialog').then((mod) => ({
      default: mod.DirectCareerCoachDialog
    })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-[#F6A03C]" />
      </div>
    )
  }
);

// This is the heaviest component - contains PDF generation libraries
const MockInterviewClient = dynamic(
  () => import('@/components/interview/MockInterviewClient'),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-[#F6A03C]" />
        <span className="ml-2">Loading Interview Module...</span>
      </div>
    )
  }
);

// Heavy market trends component
const MarketTrendsDashboard = dynamic(
  () => import('../market-trends/MarketTrendsDashboard'),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-[#F6A03C]" />
        <span className="ml-2">Loading Market Trends...</span>
      </div>
    )
  }
);

// ============================================================================
// LOADING COMPONENTS
// ============================================================================

const DialogLoadingSpinner = () => (
  <div className="flex flex-col items-center justify-center p-8 space-y-4">
    <div className="relative">
      <div className="absolute inset-0 rounded-full bg-hero-yellow/20 blur-xl animate-pulse"></div>
      <Loader2 className="h-12 w-12 animate-spin text-[#F6A03C] relative z-10" />
    </div>
    <p className="text-white">Loading...</p>
  </div>
);

// Helper type: fully expanded features
type ProcessedFeature = FeatureProps & {
  disabled: boolean;
  hasStoredResults: boolean;
  isCareerMatching: boolean;
  isResumeSelected: boolean;
  requiresJob?: boolean;
  requiresResume?: boolean;
};

function DashboardContent() {
  const [isJobDetailsOpen, setIsJobDetailsOpen] = React.useState(false);

  const {
    credits,
    resumes,
    savedJobs,
    error,
    selectedJob,
    selectedResume,
    features: dashboardFeatures,
    viewingItem,
    isDeleteDialogOpen,
    userId,
    setViewingItem,
    setIsDeleteDialogOpen,
    activeFeature,
    isMarketTrendsDialogOpen,
    setIsMarketTrendsDialogOpen,
    setSelectedResume,
    setSavedJobs,
    fetchResumes,
    setSelectedJob,
    fetchJobs,
    checkCredits
  } = useDashboardContext();

  // inline UploadJobForm
  const {
    analyzeAndSaveJob,
    job: editingJob,
    setJob: setEditingJob,
    isLoading: isUploadingJob
  } = useJobManagement({
    userId,
    onSuccess: (savedJob) => handleJobUploaded(savedJob)
  });

  const handleJobDataChange = (description: string, link: string) => {
    setEditingJob((prev) =>
      prev
        ? { ...prev, description, job_link: link }
        : {
            id: crypto.randomUUID(),
            user_id: userId,
            title: null,
            company: null,
            description,
            job_link: link,
            salary_range: null,
            location: null,
            analysis_result: {} as JobAnalysisResult,
            status: 'saved',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
    );
  };

  // resume auto-selection
  const [isUploadResumeDialogOpen, setIsUploadResumeDialogOpen] =
    useState(false);
  const handleResumeUploaded = useCallback(
    async (resume: Resume) => {
      toast({
        title: 'CV Uploaded',
        description: 'Analyzing…',
        duration: 25000
      });
      try {
        await fetch('/api/analyze-resume', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ resumeId: resume.id, fileName: resume.resume })
        });
        await fetchResumes();
        toast({ title: 'CV Analyzed', description: 'Ready!', duration: 3500 });
      } catch {
        toast({ title: 'Analysis failed', variant: 'destructive' });
      }
    },
    [fetchResumes]
  );
  useEffect(() => {
    if (resumes.length && !selectedResume) setSelectedResume(resumes[0]);
  }, [resumes, selectedResume, setSelectedResume]);
  useEffect(() => {
    if (savedJobs.length && !selectedJob) setSelectedJob(savedJobs[0]);
  }, [savedJobs, selectedJob, setSelectedJob]);

  // feature handlers
  const {
    performATSAnalysis,
    performSkillsGap,
    performCareerMatching,
    performCoverLetter,
    performMarketTrends,
    performLearningResources,
    performResumeImprovement
  } = useUnifiedFeatureHandlers();
  const {
    setSelectedJob: setFeatureSelectedJob,
    setSelectedResume: setFeatureSelectedResume,
    setFeatureResult,
    openFeatureDialog,
    setActiveFeature,
    setFeatureLoading
  } = useFeature();

  useEffect(() => {
    if (selectedResume) setFeatureSelectedResume(selectedResume);
  }, [selectedResume, setFeatureSelectedResume]);
  useEffect(() => {
    if (selectedJob) setFeatureSelectedJob(selectedJob);
  }, [selectedJob, setFeatureSelectedJob]);

  const handleActivate = async (featureTitle: string) => {
    try {
      switch (featureTitle) {
        case 'ATS':
          await performATSAnalysis();
          break;
        case 'Skill Gap Analysis':
          await performSkillsGap();
          break;
        case 'AI CV Improvement':
          await performResumeImprovement();
          break;
        case 'AI Career Matching':
          await performCareerMatching();
          break;
        case 'Cover Letter Generator': {
          // Check if cover letter is cached for selected job and resume
          if (selectedResume && selectedJob) {
            const storageKey = `CoverLetter_${selectedResume.id}_${selectedJob.id}`;
            const cachedCoverLetter = localStorage.getItem(storageKey);
            if (!cachedCoverLetter) {
              setFeatureResult('coverLetter', null!);
            }
            if (cachedCoverLetter) {
              // If cached, set the result directly and open dialog without loading
              setFeatureResult('coverLetter', JSON.parse(cachedCoverLetter));
              openFeatureDialog('coverLetter');
              setActiveFeature('coverLetter');
              setFeatureLoading('coverLetter', false);
              return;
            }
          }
          // Otherwise, show loading and open dialog before generating cover letter
          setFeatureLoading('coverLetter', true);
          openFeatureDialog('coverLetter');
          setActiveFeature('coverLetter');
          try {
            await performCoverLetter();
          } finally {
            setFeatureLoading('coverLetter', false);
          }
          break;
        }
        case 'Learning Resources':
          await performLearningResources();
          break;
        case 'Market Trends':
          await performMarketTrends();
          break;
        case 'Recruitment Agencies':
          setIsDirectRecruitmentDialogOpen(true);
          return;
        case 'AI Career Coach': {
          const ok = await checkCredits('CAREER_COACHING');
          if (!ok)
            toast({ title: 'Not enough credits', variant: 'destructive' });
          setIsDirectCareerCoachDialogOpen(true);
          return;
        }
        case 'Mock Interview':
          if (selectedResume && selectedJob) setIsInterviewActive(true);
          return;
        default:
          toast({ title: 'Unknown feature' });
      }
    } catch {
      toast({ title: 'Feature failed', variant: 'destructive' });
    }
  };

  // delete handlers
  const handleConfirmDelete = async () => {
    if (!viewingItem) return;
    try {
      console.log('Deleting item:', viewingItem);
      // Check if viewingItem is a Resume by checking for 'resume' property instead of 'CV'
      if ('resume' in viewingItem) {
        console.log('Deleting resume with ID:', viewingItem.id);
        await deleteResume(viewingItem as Resume);
        await fetchResumes();
        if (selectedResume?.id === viewingItem.id) setSelectedResume(null);
      } else {
        console.log('Deleting job with ID:', viewingItem.id);
        await deleteJob(viewingItem as Job);
        await fetchJobs();
        if (selectedJob?.id === viewingItem.id) setSelectedJob(null);
      }
      toast({ title: 'Deleted' });
    } catch (error) {
      console.error('Delete failed:', error);
      toast({ title: 'Delete failed', variant: 'destructive' });
    } finally {
      setIsDeleteDialogOpen(false);
      setViewingItem(null);
    }
  };

  // job upload success
  const handleJobUploaded = (job: Job) => {
    setSelectedJob(job);
    setSavedJobs((prev) => [job, ...prev]);
    fetchJobs();
    toast({ title: 'Job Uploaded', description: 'Analyzed and selected.' });
  };

  // build processedFeatures
  const processedFeatures = useMemo<ProcessedFeature[]>(() => {
    return dashboardFeatures.map((feature) => {
      const disabled = Boolean(
        (feature.requiresResume && !selectedResume) ||
          (feature.requiresJob && !selectedJob)
      );
      const normalized = feature.title.replace(/-/g, ' ');
      const key = normalized as keyof typeof FEATURE_TO_STORAGE_KEY;
      const storageKeyBase = FEATURE_TO_STORAGE_KEY[key];
      const hasStoredResults = Boolean(
        storageKeyBase &&
          selectedResume &&
          selectedJob &&
          localStorage.getItem(
            `${storageKeyBase}_${selectedResume.id}_${selectedJob.id}`
          )
      );
      return {
        ...feature,
        disabled,
        hasStoredResults,
        isCareerMatching: normalized === 'AI Career Matching',
        isResumeSelected: !!selectedResume,
        requiresJob: feature.requiresJob,
        requiresResume: feature.requiresResume
      };
    });
  }, [dashboardFeatures, selectedResume, selectedJob]);

  // dialogs state - these now lazy load the components
  const [isDirectRecruitmentDialogOpen, setIsDirectRecruitmentDialogOpen] =
    useState(false);
  const [isDirectCareerCoachDialogOpen, setIsDirectCareerCoachDialogOpen] =
    useState(false);
  const [isInterviewActive, setIsInterviewActive] = useState(false);

  return (
    <div className="relative isolate">
      <div className="mx-auto max-w-[1200px] px-4 pb-8 pt-20 space-y-8 text-white">
        <h1 className="font-roboto-condensed font-[700] text-[clamp(2rem,6vw,3rem)]">
          User{' '}
          <span className="text-yellow-400 text-shadow-yellow">Dashboard</span>
        </h1>
        <div className="grid gap-8 lg:grid-cols-3 lg:auto-cols-fr">
          {/* credits */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-roboto-condensed font-semibold text-white">
                Your{' '}
                <span className="text-yellow-400 text-shadow-yellow">
                  Credits
                </span>
              </h2>
              <ThemedButton
                onClick={() => {
                  // Navigate to /credits or handle buy credits action
                  window.location.href = '/credits';
                }}
                variant="primary"
                size="sm"
              >
                <Plus className="h-4 w-4" />
                Buy More Credits
              </ThemedButton>
            </div>
            <CreditBalance credits={credits} isLoading={false} error={null} />
          </div>
          {/* resumes */}
          <div className="space-y-4">
            <ResumeAnalysisSection
              resumes={resumes}
              selectedResume={selectedResume}
              onDelete={(r) => {
                if (!r.id || r.id === 'NaN') {
                  toast({
                    title: 'Invalid CV ID, cannot delete',
                    variant: 'destructive'
                  });
                  return;
                }
                setViewingItem(r);
                setIsDeleteDialogOpen(true);
              }}
              onSelect={(r, sel) => setSelectedResume(sel ? r : null)}
              onUploadResume={() => setIsUploadResumeDialogOpen(true)}
            />
          </div>
          {/* jobs + upload */}
          <div className="space-y-4">
            <JobSection
              savedJobs={savedJobs}
              selectedJob={selectedJob}
              onViewDetails={(j) => setViewingItem(j)}
              onDelete={(j) => {
                setViewingItem(j);
                setIsDeleteDialogOpen(true);
              }}
              onSelect={(j, sel) => setSelectedJob(sel ? j : null)}
              onUploadJob={() => setIsJobDetailsOpen(true)} // Add this line
            />
            <UploadJobForm
              initialJob={editingJob ?? selectedJob ?? null}
              onJobDataChange={handleJobDataChange}
              onSubmit={analyzeAndSaveJob}
              onComplete={handleJobUploaded}
              submitButtonText={
                isUploadingJob ? 'Analyzing…' : 'Upload & Analyse Job'
              }
              isLoading={isUploadingJob}
              showHeader={false} // Hide header since JobSection handles the UI
              showPreview={false} // Hide preview since JobSection handles the UI
              externalDialogOpen={isJobDetailsOpen} // Use external dialog control
              onExternalDialogClose={() => setIsJobDetailsOpen(false)} // Add close handler
            />
          </div>
        </div>
        {/* features full width */}
        <FeaturesSection
          features={processedFeatures}
          activeFeature={activeFeature}
          selectedJob={selectedJob}
          selectedResume={selectedResume}
          isResumeSelected={!!selectedResume}
          onActivate={handleActivate}
        />
        {error && <div className="text-red-400">Error: {error.message}</div>}

        {/* ============================================================================ */}
        {/* ALWAYS LOADED DIALOGS - These are lightweight and needed frequently */}
        {/* ============================================================================ */}
        <JobDetailsDialog
          isOpen={!!viewingItem && 'title' in viewingItem}
          onClose={() => setViewingItem(null)}
          job={viewingItem as Job | null}
        />
        <DeleteDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          onConfirm={handleConfirmDelete}
        />
        <UnifiedFeatureDialogs />
        <UploadResumeDialog
          isOpen={isUploadResumeDialogOpen}
          onClose={() => setIsUploadResumeDialogOpen(false)}
          onComplete={handleResumeUploaded}
          userId={userId}
        />

        {/* ============================================================================ */}
        {/* LAZY LOADED DIALOGS - These load heavy components only when needed */}
        {/* ============================================================================ */}

        {/* Market Trends - lazy loaded */}
        {SHOW_MARKET_TRENDS && isMarketTrendsDialogOpen && (
          <Dialog
            open={isMarketTrendsDialogOpen}
            onOpenChange={setIsMarketTrendsDialogOpen}
          >
            <DialogCard title="Market Trends" accent={false}>
              <Suspense fallback={<DialogLoadingSpinner />}>
                <MarketTrendsDashboard
                  jobDescription={selectedJob?.description || ''}
                  resume={selectedResume}
                  userId={userId}
                  credits={credits}
                />
              </Suspense>
            </DialogCard>
          </Dialog>
        )}

        {/* Recruitment Agencies - lazy loaded */}
        {isDirectRecruitmentDialogOpen && (
          <Suspense fallback={<DialogLoadingSpinner />}>
            <DirectRecruitmentAgenciesDialog
              isOpen={isDirectRecruitmentDialogOpen}
              onClose={() => setIsDirectRecruitmentDialogOpen(false)}
            />
          </Suspense>
        )}

        {/* Career Coach - lazy loaded */}
        {isDirectCareerCoachDialogOpen && (
          <Suspense fallback={<DialogLoadingSpinner />}>
            <DirectCareerCoachDialog
              isOpen={isDirectCareerCoachDialogOpen}
              onClose={() => setIsDirectCareerCoachDialogOpen(false)}
            />
          </Suspense>
        )}

        {/* Mock Interview - lazy loaded (heaviest component) */}
        {isInterviewActive && selectedResume && selectedJob && (
          <Dialog open={isInterviewActive} onOpenChange={setIsInterviewActive}>
            <DialogCard title="Mock Interview" accent={false}>
              <Suspense fallback={<DialogLoadingSpinner />}>
                <MockInterviewClient
                  onClose={() => setIsInterviewActive(false)}
                  resume={selectedResume}
                  job={selectedJob}
                  skipSetup={false}
                />
              </Suspense>
            </DialogCard>
          </Dialog>
        )}
      </div>
    </div>
  );
}

export interface DashboardProps {
  userId: string;
  initialFeatures: FeatureProps[];
  initialResumes: Resume[];
  initialJobs: Job[];
  initialError: PostgrestError | null;
  initialCredits: number;
  dashboardId?: string;
}

export default function Dashboard(props: DashboardProps) {
  return (
    <DashboardProvider {...props}>
      <FeatureProvider>
        <DashboardContent />
      </FeatureProvider>
    </DashboardProvider>
  );
}
